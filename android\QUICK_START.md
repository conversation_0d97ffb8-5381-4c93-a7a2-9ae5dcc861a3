# دليل البدء السريع - العقل المبدع
## Quick Start Guide - Creative Mind

### خطوات سريعة لبناء التطبيق:

#### 1. إعداد البيئة:
```bash
# تأكد من تثبيت:
# - Android Studio
# - Java 8+
# - Android SDK
```

#### 2. إعداد المشروع:
```bash
cd android

# انسخ ملف local.properties
copy local.properties.template local.properties

# عدل المسار في local.properties ليشير إلى Android SDK
```

#### 3. بناء التطبيق:
```bash
# طريقة سريعة
build_apk.bat

# أو يدوياً
gradlew.bat clean
gradlew.bat assembleRelease
```

#### 4. النتيجة:
```
ملف APK سيكون في:
app/build/outputs/apk/release/app-release.apk
```

### المشاكل الشائعة:

#### مشكلة: SDK path not found
**الحل:**
```bash
# عدل ملف local.properties
# أضف المسار الصحيح لـ Android SDK
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
```

#### مشكلة: Java version
**الحل:**
```bash
# تأكد من Java 8 أو أحدث
java -version
```

#### مشكلة: Gradle build failed
**الحل:**
```bash
# نظف المشروع
gradlew.bat clean

# أعد البناء
gradlew.bat assembleRelease
```

### الأيقونات:
- الأيقونات الحالية في drawable/ هي vector icons
- لإضافة أيقونات PNG، استخدم create_simple_icons.bat
- أو استخدم Android Studio Asset Studio

### التخصيص:
- لتغيير اسم التطبيق: عدل strings.xml
- لتغيير الألوان: عدل colors.xml  
- لتغيير الأيقونة: استبدل ملفات mipmap/

### النشر:
```bash
# للتثبيت على جهاز متصل
gradlew.bat installRelease

# لإنشاء APK موقع للنشر
# أضف keystore في build.gradle أولاً
```

### الدعم:
- الحد الأدنى: Android 5.0 (API 21)
- الهدف: Android 14 (API 34)
- دعم RTL للعربية
- WebView محسن للأداء
