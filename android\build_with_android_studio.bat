@echo off
echo ========================================
echo    بناء تطبيق العقل المبدع
echo    Building Creative Mind App
echo ========================================
echo.

echo البحث عن Android Studio...
echo Looking for Android Studio...

REM Check common Android Studio paths
set STUDIO_PATH=""
if exist "C:\Program Files\Android\Android Studio\bin\studio64.exe" (
    set STUDIO_PATH="C:\Program Files\Android\Android Studio\bin\studio64.exe"
    echo تم العثور على Android Studio في: C:\Program Files\Android\Android Studio\
)

if exist "C:\Users\<USER>\AppData\Local\Android\Sdk\tools\bin\sdkmanager.bat" (
    echo تم العثور على Android SDK
    echo Found Android SDK
)

echo.
echo فتح المشروع في Android Studio...
echo Opening project in Android Studio...

if not %STUDIO_PATH%=="" (
    echo تشغيل Android Studio...
    echo Starting Android Studio...
    start "" %STUDIO_PATH% "%CD%"
    echo.
    echo تم فتح Android Studio مع المشروع
    echo Android Studio opened with project
    echo.
    echo خطوات البناء:
    echo Build steps:
    echo 1. انتظر تحميل المشروع
    echo    Wait for project to load
    echo 2. Build -^> Generate Signed Bundle/APK
    echo 3. اختر APK
    echo    Choose APK
    echo 4. اتبع التعليمات
    echo    Follow instructions
) else (
    echo لم يتم العثور على Android Studio
    echo Android Studio not found
    echo.
    echo يرجى تثبيت Android Studio من:
    echo Please install Android Studio from:
    echo https://developer.android.com/studio
    echo.
    echo أو افتح المشروع يدوياً:
    echo Or open project manually:
    echo 1. افتح Android Studio
    echo 2. اختر "Open Project"
    echo 3. اختر مجلد: %CD%
)

echo.
echo مسار المشروع:
echo Project path:
echo %CD%
echo.
pause
