<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Creative Mind Brain Icon -->
    <group android:scaleX="0.8"
           android:scaleY="0.8"
           android:pivotX="54"
           android:pivotY="54">
        
        <!-- Brain shape with gradient effect -->
        <path
            android:pathData="M54,25.2C29.4,25.2,20.5,45.7,22,60.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1 C87.5,45.7,78.6,25.2,54,25.2z"
            android:fillColor="#c039ff" />
        
        <!-- Gradient overlay -->
        <path
            android:pathData="M54,25.2C29.4,25.2,20.5,45.7,22,60.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1 C87.5,45.7,78.6,25.2,54,25.2z">
            <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
                <gradient
                    android:startColor="#c039ff"
                    android:endColor="#6a11cb"
                    android:type="linear"
                    android:angle="135" />
            </aapt:attr>
        </path>
        
        <!-- Eyes -->
        <circle
            android:cx="46"
            android:cy="65"
            android:r="5"
            android:fillColor="#FFFFFF" />
        <circle
            android:cx="62"
            android:cy="65"
            android:r="5"
            android:fillColor="#FFFFFF" />
            
        <!-- Pupils -->
        <circle
            android:cx="46"
            android:cy="65"
            android:r="2"
            android:fillColor="#333333" />
        <circle
            android:cx="62"
            android:cy="65"
            android:r="2"
            android:fillColor="#333333" />
    </group>
</vector>
