@echo off
echo ========================================
echo    بناء تطبيق العقل المبدع بطريقة مبسطة
echo    Building Creative Mind App (Simple)
echo ========================================
echo.

REM Check if Android SDK is available
if not defined ANDROID_HOME (
    echo تحذير: متغير ANDROID_HOME غير محدد
    echo Warning: ANDROID_HOME not set
    echo.
    echo يرجى تحديد مسار Android SDK:
    echo Please set Android SDK path:
    set /p ANDROID_HOME="Enter Android SDK path: "
)

echo Android SDK: %ANDROID_HOME%
echo.

REM Check if we have gradle installed globally
where gradle >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo استخدام Gradle المثبت عالمياً...
    echo Using globally installed Gradle...
    gradle clean assembleRelease
) else (
    echo Gradle غير متوفر عالمياً
    echo Gradle not available globally
    echo.
    echo يرجى استخدام Android Studio لبناء المشروع:
    echo Please use Android Studio to build the project:
    echo 1. افتح Android Studio
    echo 2. اختر "Open Project"
    echo 3. اختر مجلد android
    echo 4. Build -^> Generate Signed Bundle/APK
    echo.
)

echo.
echo إذا تم البناء بنجاح، ستجد APK في:
echo If build successful, APK will be at:
echo app\build\outputs\apk\release\app-release.apk
echo.
pause
