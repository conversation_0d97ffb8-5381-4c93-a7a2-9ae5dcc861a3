@echo off
echo ========================================
echo    بناء APK حقيقي - العقل المبدع
echo    Building Real APK - Creative Mind
echo ========================================
echo.

REM Check for Android SDK
echo البحث عن Android SDK...
echo Looking for Android SDK...

set SDK_FOUND=0
set ANDROID_HOME=""

REM Check common SDK locations
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    set SDK_FOUND=1
    echo ✓ تم العثور على Android SDK في: %ANDROID_HOME%
)

if exist "C:\Android\Sdk" (
    set ANDROID_HOME=C:\Android\Sdk
    set SDK_FOUND=1
    echo ✓ تم العثور على Android SDK في: %ANDROID_HOME%
)

if exist "C:\Program Files\Android\Sdk" (
    set ANDROID_HOME=C:\Program Files\Android\Sdk
    set SDK_FOUND=1
    echo ✓ تم العثور على Android SDK في: %ANDROID_HOME%
)

if %SDK_FOUND%==0 (
    echo ❌ لم يتم العثور على Android SDK
    echo ❌ Android SDK not found
    echo.
    echo يرجى تثبيت Android Studio أولاً من:
    echo Please install Android Studio first from:
    echo https://developer.android.com/studio
    echo.
    pause
    exit /b 1
)

echo.
echo تحقق من Java...
echo Checking for Java...

java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Java غير متوفر
    echo ❌ Java not available
    echo.
    echo يرجى تثبيت Java JDK من:
    echo Please install Java JDK from:
    echo https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Java متوفر
    echo ✓ Java available
    java -version
)

echo.
echo إعداد متغيرات البيئة...
echo Setting environment variables...

set ANDROID_HOME=%ANDROID_HOME%
set PATH=%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\build-tools\34.0.0;%PATH%

echo.
echo بناء APK...
echo Building APK...

REM Try to use gradlew if available
if exist "gradlew.bat" (
    echo استخدام Gradle Wrapper...
    echo Using Gradle Wrapper...
    
    gradlew.bat clean
    gradlew.bat assembleRelease
    
    if %ERRORLEVEL% equ 0 (
        echo.
        echo ✅ تم بناء APK بنجاح!
        echo ✅ APK built successfully!
        echo.
        echo مكان APK:
        echo APK location:
        dir app\build\outputs\apk\release\*.apk
    ) else (
        echo ❌ فشل في بناء APK باستخدام Gradle
        echo ❌ Failed to build APK using Gradle
    )
) else (
    echo Gradle Wrapper غير متوفر، محاولة طريقة بديلة...
    echo Gradle Wrapper not available, trying alternative method...
    
    REM Try direct build tools
    if exist "%ANDROID_HOME%\build-tools\34.0.0\aapt.exe" (
        echo استخدام أدوات البناء المباشرة...
        echo Using direct build tools...
        
        REM Create directories
        mkdir app\build\outputs\apk\release 2>nul
        
        REM Simple APK creation (this is a simplified approach)
        echo إنشاء APK مبسط...
        echo Creating simplified APK...
        
        REM This would require more complex build process
        echo ملاحظة: يتطلب إعداد أكثر تعقيداً
        echo Note: Requires more complex setup
        
        echo.
        echo الحل الأفضل: استخدام Android Studio
        echo Best solution: Use Android Studio
        echo 1. افتح Android Studio
        echo 2. اختر "Open Project"
        echo 3. اختر مجلد: %CD%
        echo 4. Build -^> Generate Signed Bundle/APK
    ) else (
        echo ❌ أدوات البناء غير متوفرة
        echo ❌ Build tools not available
    )
)

echo.
echo ========================================
echo خيارات أخرى لإنشاء APK:
echo Other options to create APK:
echo ========================================
echo.
echo 1. Android Studio (الأفضل):
echo    - تحميل: https://developer.android.com/studio
echo    - فتح المشروع وبناء APK
echo.
echo 2. Online APK Builder:
echo    - اذهب إلى: https://www.appsgeyser.com/
echo    - ارفع ملفات المشروع
echo    - احصل على APK
echo.
echo 3. Cordova/PhoneGap:
echo    - تثبيت Node.js
echo    - تثبيت Cordova
echo    - بناء APK
echo.
pause
