@echo off
echo ========================================
echo    إنشاء APK حقيقي - العقل المبدع
echo    Creating Real APK - Creative Mind
echo ========================================
echo.

echo تحقق من Node.js...
echo Checking for Node.js...

where node >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✓ Node.js متوفر
    echo ✓ Node.js available
    node --version
    
    echo.
    echo تثبيت Cordova...
    echo Installing Cordova...
    
    npm install -g cordova
    
    if %ERRORLEVEL% equ 0 (
        echo.
        echo إنشاء مشروع Cordova...
        echo Creating Cordova project...
        
        cd ..
        cordova create creative-mind-cordova com.creativemind.app "العقل المبدع"
        
        if %ERRORLEVEL% equ 0 (
            cd creative-mind-cordova
            
            echo.
            echo نسخ ملفات التطبيق...
            echo Copying app files...
            
            copy ..\kfojo.html www\index.html
            copy ..\pwa-manifest.json www\manifest.json
            
            echo.
            echo إضافة منصة Android...
            echo Adding Android platform...
            
            cordova platform add android
            
            if %ERRORLEVEL% equ 0 (
                echo.
                echo بناء APK...
                echo Building APK...
                
                cordova build android
                
                if %ERRORLEVEL% equ 0 (
                    echo.
                    echo ✅ تم إنشاء APK بنجاح!
                    echo ✅ APK created successfully!
                    echo.
                    echo مكان APK:
                    echo APK location:
                    echo platforms\android\app\build\outputs\apk\debug\app-debug.apk
                    
                    REM Copy APK to main directory
                    copy platforms\android\app\build\outputs\apk\debug\app-debug.apk ..\creative-mind-app.apk
                    
                    echo.
                    echo تم نسخ APK إلى:
                    echo APK copied to:
                    echo ..\creative-mind-app.apk
                ) else (
                    echo ❌ فشل في بناء APK
                    echo ❌ Failed to build APK
                )
            ) else (
                echo ❌ فشل في إضافة منصة Android
                echo ❌ Failed to add Android platform
            )
        ) else (
            echo ❌ فشل في إنشاء مشروع Cordova
            echo ❌ Failed to create Cordova project
        )
    ) else (
        echo ❌ فشل في تثبيت Cordova
        echo ❌ Failed to install Cordova
    )
) else (
    echo ❌ Node.js غير متوفر
    echo ❌ Node.js not available
    echo.
    echo يرجى تثبيت Node.js من:
    echo Please install Node.js from:
    echo https://nodejs.org/
    echo.
    echo ثم أعد تشغيل هذا الملف
    echo Then run this file again
)

echo.
echo ========================================
echo بدائل أخرى لإنشاء APK:
echo Other alternatives to create APK:
echo ========================================
echo.
echo 1. Android Studio (الأفضل):
echo    Android Studio (Best):
echo    - تحميل: https://developer.android.com/studio
echo    - فتح مجلد android في المشروع
echo    - Build -^> Generate Signed Bundle/APK
echo.
echo 2. Online APK Builders:
echo    - AppsGeyser.com
echo    - Appy Pie
echo    - BuildFire
echo.
echo 3. PWA (تم إنشاؤه):
echo    PWA (Already created):
echo    - افتح kfojo.html في Chrome
echo    - اضغط "إضافة إلى الشاشة الرئيسية"
echo    - سيعمل مثل التطبيق
echo.
pause
