<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_background"
    android:gravity="center">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/splash_logo"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="24dp"
            android:src="@drawable/ic_launcher_foreground"
            android:contentDescription="@string/app_name" />

        <TextView
            android:id="@+id/splash_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="28sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:letterSpacing="0.02"
            android:shadowColor="@color/purple_primary"
            android:shadowDx="0"
            android:shadowDy="2"
            android:shadowRadius="8" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/app_version"
            android:textColor="@color/purple_light"
            android:textSize="14sp"
            android:alpha="0.8" />

    </LinearLayout>

    <!-- Loading indicator -->
    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="80dp"
        android:indeterminateTint="@color/purple_accent"
        style="?android:attr/progressBarStyle" />

</RelativeLayout>
