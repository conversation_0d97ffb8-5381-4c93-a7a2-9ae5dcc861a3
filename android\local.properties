# This file contains the path to the Android SDK
# Copy this file to local.properties and update the sdk.dir path

# For Windows (example):
# sdk.dir=C\:\\Users\\YourUsername\\AppData\\Local\\Android\\Sdk

# For macOS (example):
# sdk.dir=/Users/<USER>/Library/Android/sdk

# For Linux (example):
# sdk.dir=/home/<USER>/Android/Sdk

# Replace with your actual Android SDK path:
sdk.dir=C\:\\Users\\ThinkPad\\AppData\\Local\\Android\\Sdk
