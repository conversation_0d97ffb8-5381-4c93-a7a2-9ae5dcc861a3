@echo off
echo ========================================
echo    بناء تطبيق العقل المبدع - Android APK
echo    Building Creative Mind Android APK
echo ========================================
echo.

REM Check if gradlew exists
if not exist "gradlew.bat" (
    echo خطأ: ملف gradlew.bat غير موجود
    echo Error: gradlew.bat not found
    pause
    exit /b 1
)

echo 1. تنظيف المشروع... / Cleaning project...
call gradlew.bat clean
if %ERRORLEVEL% neq 0 (
    echo خطأ في تنظيف المشروع
    echo Error cleaning project
    pause
    exit /b 1
)

echo.
echo 2. بناء APK للإنتاج... / Building release APK...
call gradlew.bat assembleRelease
if %ERRORLEVEL% neq 0 (
    echo خطأ في بناء APK
    echo Error building APK
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم بناء التطبيق بنجاح!
echo APK built successfully!
echo ========================================
echo.
echo موقع ملف APK:
echo APK Location:
echo app\build\outputs\apk\release\app-release.apk
echo.

REM Check if APK file exists
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✓ تم العثور على ملف APK
    echo ✓ APK file found
    
    REM Get file size
    for %%A in ("app\build\outputs\apk\release\app-release.apk") do (
        echo حجم الملف: %%~zA بايت
        echo File size: %%~zA bytes
    )
) else (
    echo ✗ لم يتم العثور على ملف APK
    echo ✗ APK file not found
)

echo.
echo لتثبيت التطبيق على الجهاز:
echo To install on device:
echo gradlew.bat installRelease
echo.
pause
