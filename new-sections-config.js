// أقسام جديدة مقترحة لتطبيق العقل المبدع
// New Suggested Sections for Creative Mind App

const newSectionsConfig = {
    health: {
        name: "صحة", 
        key: 'health',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zM7 3a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H7zm1 7a4 4 0 0 0-4 4h8a4 4 0 0 0-4-4z"/></svg>`,
        placeholder: "اكتب سؤالاً صحياً أو نمط حياة تريد تحسينه...",
        suggestions: ["تمارين للعمل من المنزل", "نظام غذائي لزيادة الطاقة", "طرق تقليل التوتر", "تحسين جودة النوم", "تمارين لتقوية المناعة"],
        
        mainPrompt: `أنت طبيب ومدرب صحة معتمد متخصص في الطب الوقائي ونمط الحياة الصحي. قدم 5 نصائح أو حلول صحية للموضوع: [USER_INPUT]

معايير النصائح الصحية:
- اعتمد على الأدلة العلمية والدراسات الطبية
- قدم حلولاً عملية وقابلة للتطبيق في الحياة اليومية
- راعي الفروق الفردية والحالات الصحية المختلفة
- اجعل النصائح آمنة ومناسبة للعامة
- أضف تحذيرات عند الحاجة لاستشارة طبية

يجب أن يكون الرد باللغة العربية حصراً مع التأكيد على أن هذه نصائح عامة وليست بديلاً عن الاستشارة الطبية.`,

        expandPrompt: `بالتفصيل وباللغة العربية، اشرح النصيحة الصحية '[TITLE]'. 

قدم:
1. الأساس العلمي وراء هذه النصيحة
2. خطة تطبيق عملية مع جدول زمني
3. الفوائد المتوقعة على المدى القصير والطويل
4. احتياطات ومحاذير مهمة
5. طرق قياس التقدم والنتائج
6. بدائل للأشخاص ذوي الظروف الخاصة
7. نصائح للاستمرارية والدافعية

تذكير: هذه نصائح عامة وليست بديلاً عن الاستشارة الطبية المتخصصة.`,
        
        expandButtonText: "عرض الخطة الصحية"
    },

    technology: {
        name: "تقنية", 
        key: 'technology',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2zm13 2.383-4.708 2.825L15 11.105V5.383zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741zM1 11.105l4.708-2.897L1 5.383v5.722z"/></svg>`,
        placeholder: "اكتب مشكلة تقنية أو فكرة تطبيق تريد تطويرها...",
        suggestions: ["تطبيق جوال للتجارة", "موقع ويب تفاعلي", "أتمتة المهام اليومية", "نظام إدارة المحتوى", "تطبيق ذكاء اصطناعي"],
        
        mainPrompt: `أنت مطور ومهندس برمجيات خبير متخصص في التقنيات الحديثة والحلول المبتكرة. قدم 5 حلول أو أفكار تقنية للموضوع: [USER_INPUT]

معايير الحلول التقنية:
- استخدم أحدث التقنيات والأدوات المتاحة
- راعي قابلية التوسع والأمان
- اقترح حلولاً عملية وقابلة للتطبيق
- فكر في تجربة المستخدم وسهولة الاستخدام
- اعتبر التكلفة والوقت المطلوب للتطوير

يجب أن يكون الرد باللغة العربية حصراً مع ذكر التقنيات المستخدمة.`,

        expandPrompt: `بالتفصيل وباللغة العربية، اشرح الحل التقني '[TITLE]'. 

قدم:
1. وصف تقني مفصل للحل
2. التقنيات والأدوات المطلوبة
3. خطة التطوير مرحلة بمرحلة
4. متطلبات النظام والبنية التحتية
5. تقدير الوقت والتكلفة
6. التحديات التقنية المحتملة وحلولها
7. خطة الاختبار والنشر
8. خطة الصيانة والتطوير المستقبلي

استخدم مصطلحات تقنية دقيقة مع شرح مبسط للمفاهيم المعقدة.`,
        
        expandButtonText: "عرض الحل التقني"
    },

    art: {
        name: "فن", 
        key: 'art',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M9.5 0a.5.5 0 0 1 .5.5 1.5 1.5 0 1 1-3 0 .5.5 0 0 1 .5-.5zM6.5 4.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-3z"/><path d="M8 1a7 7 0 1 0 0 14A7 7 0 0 0 8 1zM7 2.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/></svg>`,
        placeholder: "اكتب فكرة فنية أو مشروع إبداعي تريد تطويره...",
        suggestions: ["لوحة فنية معاصرة", "تصميم شعار مبتكر", "مشروع فن رقمي", "معرض فني تفاعلي", "عمل نحتي حديث"],
        
        mainPrompt: `أنت فنان ومصمم محترف متخصص في الفنون البصرية والتصميم الإبداعي. قدم 5 أفكار فنية مبتكرة للموضوع: [USER_INPUT]

معايير الأفكار الفنية:
- امزج بين التقنيات التقليدية والحديثة
- راعي التأثير البصري والرسالة الفنية
- اقترح مواد وأدوات متنوعة
- فكر في الجمهور المستهدف والمكان
- اعتبر الجانب العملي والميزانية

يجب أن يكون الرد باللغة العربية حصراً مع وصف بصري دقيق.`,

        expandPrompt: `بالتفصيل وباللغة العربية، اشرح المشروع الفني '[TITLE]'. 

قدم:
1. المفهوم الفني والرسالة المراد إيصالها
2. قائمة مفصلة بالمواد والأدوات المطلوبة
3. خطوات التنفيذ مع الرسوم التوضيحية (وصفياً)
4. التقنيات الفنية المستخدمة
5. اقتراحات للألوان والتكوين
6. طرق العرض والتقديم
7. التكلفة التقديرية والوقت المطلوب
8. نصائح للمبتدئين والمحترفين

استخدم لغة فنية دقيقة مع شرح واضح للمصطلحات.`,
        
        expandButtonText: "عرض دليل التنفيذ"
    },

    travel: {
        name: "سفر", 
        key: 'travel',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/></svg>`,
        placeholder: "اكتب وجهة سفر أو نوع رحلة تريد التخطيط لها...",
        suggestions: ["رحلة عائلية اقتصادية", "سفر مغامرات", "سياحة ثقافية", "رحلة شهر عسل", "سفر عمل مريح"],
        
        mainPrompt: `أنت خبير سفر ومرشد سياحي محترف متخصص في التخطيط للرحلات المخصصة. قدم 5 خطط سفر مبتكرة للموضوع: [USER_INPUT]

معايير خطط السفر:
- راعي الميزانيات المختلفة (اقتصادي، متوسط، فاخر)
- اقترح أنشطة متنوعة ومناسبة للفئة المستهدفة
- فكر في الجوانب العملية (التأشيرات، الطقس، الأمان)
- اعتبر التفضيلات الثقافية والدينية
- اقترح بدائل للظروف الطارئة

يجب أن يكون الرد باللغة العربية حصراً مع معلومات عملية مفيدة.`,

        expandPrompt: `بالتفصيل وباللغة العربية، اشرح خطة السفر '[TITLE]'. 

قدم:
1. برنامج يومي مفصل للرحلة
2. قائمة بالأماكن السياحية والأنشطة
3. اقتراحات للإقامة والمواصلات
4. تقدير شامل للتكاليف
5. نصائح للتحضير والحجوزات
6. معلومات عن الثقافة المحلية والعادات
7. قائمة بالأشياء الضرورية للحمل
8. خطة طوارئ ومعلومات الاتصال المهمة

اجعل الخطة عملية وقابلة للتطبيق مع مراعاة الجوانب الأمنية.`,
        
        expandButtonText: "عرض خطة السفر"
    }
};

// Export for use in main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = newSectionsConfig;
} else if (typeof window !== 'undefined') {
    window.newSectionsConfig = newSectionsConfig;
}
