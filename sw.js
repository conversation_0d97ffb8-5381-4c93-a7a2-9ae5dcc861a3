// Service Worker for Creative Mind App
// العقل المبدع - Service Worker

const CACHE_NAME = 'creative-mind-v2.0';
const urlsToCache = [
  './',
  './kfojo.html',
  './pwa-manifest.json',
  'https://cdn.tailwindcss.com',
  'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap',
  'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js',
  'https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js',
  'https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js',
  'https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js'
];

// Install event
self.addEventListener('install', function(event) {
  console.log('Service Worker: Installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        console.log('Service Worker: Caching files');
        return cache.addAll(urlsToCache);
      })
      .catch(function(error) {
        console.log('Service Worker: Cache failed', error);
      })
  );
});

// Fetch event
self.addEventListener('fetch', function(event) {
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Return cached version or fetch from network
        if (response) {
          console.log('Service Worker: Serving from cache', event.request.url);
          return response;
        }
        
        console.log('Service Worker: Fetching from network', event.request.url);
        return fetch(event.request).then(function(response) {
          // Check if valid response
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }

          // Clone the response
          var responseToCache = response.clone();

          caches.open(CACHE_NAME)
            .then(function(cache) {
              cache.put(event.request, responseToCache);
            });

          return response;
        });
      })
  );
});

// Activate event
self.addEventListener('activate', function(event) {
  console.log('Service Worker: Activating...');
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Push notification event (for future use)
self.addEventListener('push', function(event) {
  console.log('Service Worker: Push received');
  
  const options = {
    body: 'لديك أفكار جديدة في انتظارك!',
    icon: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath fill="%23a78bfa" d="M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z"/%3E%3Ccircle cx="42" cy="55" r="5" fill="white"/%3E%3Ccircle cx="58" cy="55" r="5" fill="white"/%3E%3C/svg%3E',
    badge: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath fill="%23a78bfa" d="M50,15.2C25.4,15.2,16.5,35.7,18,50.7c1.4,14,13.2,34.1,32,34.1s30.6-20.1,32-34.1C83.5,35.7,74.6,15.2,50,15.2z"/%3E%3Ccircle cx="42" cy="55" r="5" fill="white"/%3E%3Ccircle cx="58" cy="55" r="5" fill="white"/%3E%3C/svg%3E',
    vibrate: [200, 100, 200],
    tag: 'creative-mind-notification'
  };

  event.waitUntil(
    self.registration.showNotification('العقل المبدع', options)
  );
});

// Background sync (for future use)
self.addEventListener('sync', function(event) {
  console.log('Service Worker: Background sync', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Perform background tasks
      console.log('Service Worker: Performing background sync')
    );
  }
});

console.log('Service Worker: Loaded successfully for Creative Mind App v2.0');
