<?xml version="1.0" encoding="utf-8"?> 
<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
    package="com.creativemind.app" 
    android:versionCode="1" 
    android:versionName="2.0" 
    android:compileSdkVersion="34" 
    android:targetSdkVersion="34"> 
 
    <uses-permission android:name="android.permission.INTERNET" /> 
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> 
 
    <application 
        android:allowBackup="true" 
        android:icon="@drawable/ic_launcher" 
        android:label="العقل المبدع" 
        android:theme="@android:style/Theme.Material.Light"> 
 
        <activity 
            android:name="com.creativemind.app.SplashActivity" 
            android:exported="true" 
            android:screenOrientation="portrait"> 
            <intent-filter> 
                <action android:name="android.intent.action.MAIN" /> 
                <category android:name="android.intent.category.LAUNCHER" /> 
            </intent-filter> 
        </activity> 
 
        <activity 
            android:name="com.creativemind.app.MainActivity" 
            android:exported="false" 
            android:screenOrientation="portrait" /> 
 
    </application> 
</manifest> 
