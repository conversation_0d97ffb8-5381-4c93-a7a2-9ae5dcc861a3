// Enhanced Prompts Configuration for العقل المبدع
// تكوين محسن للـ Prompts لتطبيق العقل المبدع

const enhancedCategoryConfig = {
    creative: {
        name: "إبداع", 
        key: 'creative',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8 1a7 7 0 1 1 0 14A7 7 0 0 1 8 1zM7 3a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H7zm1 7a4 4 0 0 0-4 4h8a4 4 0 0 0-4-4z"/></svg>`,
        placeholder: "اكتب موضوعًا لإلهامك (مثال: قصة عن المستقبل)...",
        suggestions: ["فكرة لتطبيق جديد", "اسم لمشروع تجاري", "اقتباسات ملهمة", "حلول مبتكرة للمشاكل", "أفكار محتوى إبداعي"],
        
        mainPrompt: `أنت خبير إبداع عالمي متخصص في التفكير خارج الصندوق. مهمتك إنشاء 5 أفكار مبتكرة ومتنوعة للموضوع: [USER_INPUT]

متطلبات الإجابة:
- كل فكرة يجب أن تكون فريدة ومختلفة عن الأخرى
- استخدم تقنيات الإبداع: العصف الذهني، التفكير الجانبي، الدمج بين المجالات
- اجعل كل فكرة قابلة للتطبيق عملياً
- أضف عنصر الإبهار والجدة
- استخدم أمثلة من الواقع عند الحاجة

يجب أن يكون الرد باللغة العربية حصراً.`,

        expandPrompt: `بالتفصيل وباللغة العربية، قم بتطوير فكرة '[TITLE]'. 

قدم لي:
1. شرح مفصل للفكرة وكيفية عملها
2. خطة تنفيذ عملية مع الخطوات الأولى
3. تحديد الجمهور المستهدف والفوائد
4. التحديات المحتملة وكيفية التغلب عليها
5. أمثلة مشابهة من الواقع للاستلهام

استخدم أسلوباً واضحاً ومنظماً مع أمثلة عملية.`,
        
        expandButtonText: "طور الفكرة بالتفصيل"
    },

    story: {
        name: "قصة", 
        key: 'story',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8.5 2.687c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.993v12.131c-.816-.54-1.842-.64-2.836-.534C4.14 2.71 3.054 3.14 2.1 3.515V2.828c.885-.37 2.154-.769 3.388-.985 1.234-.217 2.522.256 3.447.854z"/></svg>`,
        placeholder: "اكتب فكرة قصة (مثال: مستكشف يجد مدينة مفقودة)...",
        suggestions: ["رائد فضاء وحيد", "جريمة في قطار فاخر", "مغامرة في عالم سحري", "قصة حب في زمن الحرب", "لغز في مكتبة قديمة"],
        
        mainPrompt: `أنت روائي محترف متمكن من فنون السرد العربي الحديث. اكتب 3 بدايات قصصية متنوعة بأسلوب '[NARRATIVE_STYLE]' للفكرة: [USER_INPUT]

معايير الكتابة:
- استخدم تقنيات السرد المتقدمة (الحوار، الوصف الحسي، التشويق)
- اجعل كل بداية تنتمي لنوع أدبي مختلف (دراما، تشويق، خيال علمي، إلخ)
- ابدأ بمشهد مثير أو حوار جذاب
- اطرح سؤالاً محورياً يدفع القارئ للمتابعة
- استخدم اللغة العربية الفصحى المعاصرة

كل بداية: 100-120 كلمة
التركيز على: الشخصية + الصراع + الأجواء + عنصر التشويق

يجب أن يكون الرد باللغة العربية حصراً.`,

        expandPrompt: `أنت روائي محترف وباللغة العربية حصرًا. أكمل القصة التالية بفقرة إضافية (حوالي 150 كلمة) بنفس أسلوب السرد '[NARRATIVE_STYLE]'، مع:

- تطوير الأحداث بشكل منطقي ومثير
- تعميق الصراع وإضافة طبقات جديدة
- الحفاظ على نفس الأجواء والشخصيات
- إضافة عناصر تشويق جديدة
- استخدام حوارات طبيعية ومعبرة

القصة حتى الآن هي: '[EXISTING_TEXT]'`,
        
        expandButtonText: "أكمل القصة"
    },

    kitchen: {
        name: "مطبخ", 
        key: 'kitchen',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8 1.5a2.5 2.5 0 0 1 2.5 2.5V5h-5v-.5A2.5 2.5 0 0 1 8 1.5z"/><path d="M1.884 6.784a2.5 2.5 0 0 1 2.22-1.284h7.792a2.5 2.5 0 0 1 2.22 1.284A2.5 2.5 0 0 1 11.5 10.5h-7a2.5 2.5 0 0 1-2.616-3.716zM14 11.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11.5V10h12v1.5z"/></svg>`,
        placeholder: "اكتب المكونات المتوفرة لديك (مثال: دجاج، أرز)...",
        suggestions: ["لحم، بطاطس، بصل", "بيض، جبن، خبز", "معكرونة، صلصة طماطم", "أرز، خضار، دجاج", "سمك، ليمون، أعشاب"],
        
        mainPrompt: `أنت شيف عالمي خبير في فنون الطبخ والابتكار الغذائي. مهمتك ابتكار 5 وصفات متنوعة باستخدام المكونات: [USER_INPUT]

معايير الابتكار:
- امزج بين المطابخ العالمية والعربية
- اقترح تقنيات طبخ مختلفة (شوي، قلي، طبخ بطيء، إلخ)
- راعي التوازن الغذائي والطعم
- أضف لمسات إبداعية في التقديم
- اقترح بدائل للمكونات غير المتوفرة

يجب أن يكون الرد باللغة العربية حصراً.`,

        expandPrompt: `بالتفصيل وباللغة العربية، قدم لي طريقة عمل وصفة '[TITLE]'. 

يجب أن تتضمن:
1. قائمة دقيقة ومفصلة للمكونات مع الكميات
2. خطوات التحضير مرقمة بوضوح ودقة
3. نصائح احترافية لضمان نجاح الطبق
4. اقتراحات للتقديم والتزيين
5. معلومات غذائية مفيدة
6. بدائل للمكونات الصعبة

استخدم أسلوباً واضحاً ومفصلاً مناسب للطباخين من جميع المستويات.`,
        
        expandButtonText: "عرض الوصفة بالتفصيل"
    },

    educational: {
        name: "تعليم", 
        key: 'educational',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M8.5 5.6a.5.5 0 1 0-1 0v3.279l-1.42-1.42a.5.5 0 0 0-.708.708l2.122 2.12a.5.5 0 0 0 .707 0l2.122-2.12a.5.5 0 0 0-.707-.708L8.5 8.879V5.6z"/><path d="M1 2.828c.885-.37 2.154-.769 3.388-.985 1.234-.217 2.522.256 3.447.854.925.598 1.943.933 2.98.583 1.037-.35 2.122-.733 3.142-.828.985-.094 1.99.165 2.829.583v9.932c-.885.37-2.154.769-3.388.985-1.234-.217-2.522-.256-3.447-.854-.925-.598-1.943-.933-2.98-.583-1.037.35-2.122-.733-3.142.828-.985.094-1.99-.165-2.829-.583V2.828z"/></svg>`,
        placeholder: "اكتب مفهومًا تريد شرحه (مثال: الجاذبية الأرضية)...",
        suggestions: ["كيف تعمل البراكين؟", "شرح نظرية فيثاغورس", "ما هو الذكاء الاصطناعي؟", "كيف يعمل الإنترنت؟", "مبادئ الفيزياء الكمية"],
        
        mainPrompt: `أنت خبير تعليمي متخصص في التعلم التفاعلي والتبسيط العلمي. مهمتك تصميم 5 طرق مبتكرة لشرح: [USER_INPUT]

استراتيجيات التعليم:
- استخدم التشبيهات والأمثلة من الحياة اليومية
- اطبق نظريات التعلم الحديثة (البصري، السمعي، الحركي)
- اجعل التعلم تفاعلياً وممتعاً
- راعي الفروق الفردية والأعمار المختلفة
- استخدم القصص والألعاب التعليمية

يجب أن يكون الرد باللغة العربية حصراً.`,

        expandPrompt: `بالتفصيل وباللغة العربية حصرًا، اشرح طريقة '[TITLE]'. 

قدم:
1. شرحاً مبسطاً وواضحاً للمفهوم
2. مثالاً عملياً أو تشبيهاً من الحياة اليومية
3. نشاطاً تفاعلياً لترسيخ المفهوم
4. أسئلة تقييم لقياس الفهم
5. مصادر إضافية للتعلم
6. نصائح للمعلمين والآباء

استخدم لغة بسيطة ومناسبة للفئة المستهدفة.`,
        
        expandButtonText: "عرض الشرح التفصيلي"
    },

    project: {
        name: "مشاريع",
        key: 'project',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M12.5 7a.5.5 0 0 1 0-1h1v-1h-1a.5.5 0 0 1 0-1h1V3h-1a.5.5 0 0 1 0-1h2.5a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-2.5a.5.5 0 0 1-.5-.5z"/><path d="M1 10.5a.5.5 0 0 1 .5-.5h1V9h-1a.5.5 0 0 1 0-1h1V7h-1a.5.5 0 0 1 0-1h1V5h-1a.5.5 0 0 1 0-1h2.5A.5.5 0 0 1 4 4.5v6a.5.5 0 0 1-.5.5H1.5a.5.5 0 0 1-.5-.5z"/><path d="M6 13a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v10z"/></svg>`,
        placeholder: "اكتب فكرة مشروع (مثال: مقهى متخصص بالكتب)...",
        suggestions: ["تطبيق للياقة البدنية", "متجر إلكتروني للمنتجات اليدوية", "خدمة توصيل وجبات صحية", "منصة تعليم أونلاين", "مشروع إعادة تدوير"],

        mainPrompt: `أنت مستشار ريادة أعمال خبير في تحليل الأسواق وتطوير النماذج التجارية. حوّل الفكرة: [USER_INPUT] إلى 5 مشاريع تجارية قابلة للتطبيق

معايير التحليل:
- ادرس جدوى السوق والطلب
- حدد نموذج الربح والتكاليف المتوقعة
- اقترح استراتيجيات تسويق مبتكرة
- راعي التطورات التكنولوجية الحديثة
- فكر في قابلية التوسع والنمو

يجب أن يكون الرد باللغة العربية حصراً.`,

        expandPrompt: `بالتفصيل وباللغة العربية حصرًا، ضع خطة عمل مصغرة لمشروع '[TITLE]'.

يجب أن تتضمن:
1. تحليل بسيط للسوق والجمهور المستهدف
2. المنتجات/الخدمات الأساسية بالتفصيل
3. نموذج الأعمال وطرق تحقيق الربح
4. استراتيجية التسويق والوصول للعملاء
5. التكاليف المتوقعة ورأس المال المطلوب
6. خطة التنفيذ على مراحل زمنية
7. المخاطر المحتملة وكيفية التعامل معها
8. مؤشرات النجاح وطرق القياس

قدم خطة عملية وقابلة للتطبيق مع أرقام تقديرية واقعية.`,

        expandButtonText: "عرض خطة العمل"
    },

    dealings: {
        name: "تعاملات",
        key: 'dealings',
        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16"><path d="M14 1a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H4.414A2 2 0 0 0 3 11.586l-2 2V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12.793a.5.5 0 0 0 .854.353l2.853-2.853A1 1 0 0 1 4.414 12H14a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/><path d="M5 6a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/></svg>`,
        placeholder: "اكتب موقفًا اجتماعيًا (مثال: كيفية بدء حوار)...",
        suggestions: ["الرد على نقد غير بناء", "طلب زيادة في الراتب", "الاعتذار بشكل فعال", "التعامل مع الصراعات", "بناء علاقات مهنية"],

        mainPrompt: `أنت خبير علم النفس الاجتماعي والتواصل الإنساني. قدم 5 استراتيجيات ذكية للتعامل مع: [USER_INPUT]

أسس التواصل الفعال:
- اطبق مبادئ علم النفس السلوكي
- استخدم تقنيات التواصل اللاعنفي
- راعي الذكاء العاطفي والاجتماعي
- اقترح حلولاً عملية وقابلة للتطبيق
- احترم الثقافة العربية والقيم الاجتماعية

يجب أن يكون الرد باللغة العربية حصراً.`,

        expandPrompt: `بالتفصيل وباللغة العربية حصرًا، اشرح الاستراتيجية '[TITLE]'.

وضح:
1. السبب النفسي أو الاجتماعي وراء فعالية هذه الطريقة
2. خطوات التطبيق العملي بالتفصيل
3. أمثلة على حوارات واقعية (سيناريوهات)
4. العبارات المحددة المقترحة
5. التوقيت المناسب لاستخدام هذه الاستراتيجية
6. النتائج المتوقعة والفوائد
7. الأخطاء الشائعة التي يجب تجنبها
8. نصائح للتطبيق الناجح

قدم أمثلة عملية وحوارات كاملة توضح كيفية تطبيق الاستراتيجية كلمة بكلمة.`,

        expandButtonText: "شرح الاستراتيجية"
    }
};

// Export for use in main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = enhancedCategoryConfig;
} else if (typeof window !== 'undefined') {
    window.enhancedCategoryConfig = enhancedCategoryConfig;
}
