<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme -->
    <style name="Theme.CreativeMind" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/purple_primary</item>
        <item name="colorPrimaryVariant">@color/purple_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/purple_accent</item>
        <item name="colorSecondaryVariant">@color/purple_secondary</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/status_bar</item>
        <item name="android:navigationBarColor">@color/background_dark</item>
        
        <!-- Background colors -->
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="colorSurface">@color/background_dark</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
        
        <!-- Window flags -->
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o_mr1">false</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.CreativeMind.Splash" parent="Theme.CreativeMind">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>
