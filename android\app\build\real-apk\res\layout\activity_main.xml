<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="24dp"
    android:background="#FAFAFA">

    <!-- عنوان التطبيق -->
    <TextView
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="العقل المبدع"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="#222"
        android:layout_marginBottom="32dp" />

    <!-- زر رئيسي -->
    <Button
        android:id="@+id/startButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="ابدأ الآن"
        android:textSize="20sp"
        android:backgroundTint="#2196F3"
        android:textColor="#FFF"
        android:layout_marginBottom="16dp" />

    <!-- حقل نصي -->
    <EditText
        android:id="@+id/inputField"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="أدخل نصًا..."
        android:textAlignment="viewStart"
        android:layout_marginBottom="16dp" />

    <!-- زر ثانوي -->
    <Button
        android:id="@+id/secondaryButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="زر إضافي"
        android:textSize="18sp"
        android:backgroundTint="#4CAF50"
        android:textColor="#FFF" />

</LinearLayout>
