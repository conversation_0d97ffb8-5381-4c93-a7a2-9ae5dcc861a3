package com.creativemind.app;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {

    private TextView titleText;
    private Button startButton;
    private EditText inputField;
    private Button secondaryButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        titleText = findViewById(R.id.titleText);
        startButton = findViewById(R.id.startButton);
        inputField = findViewById(R.id.inputField);
        secondaryButton = findViewById(R.id.secondaryButton);

        startButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String input = inputField.getText().toString();
                Toast.makeText(MainActivity.this, "تم الضغط على ابدأ الآن: " + input, Toast.LENGTH_SHORT).show();
            }
        });

        secondaryButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Toast.makeText(MainActivity.this, "تم الضغط على الزر الإضافي", Toast.LENGTH_SHORT).show();
            }
        });
    }
}
