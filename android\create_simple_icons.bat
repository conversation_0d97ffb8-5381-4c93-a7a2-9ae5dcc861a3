@echo off
echo إنشاء أيقونات بسيطة للتطبيق...
echo Creating simple icons for the app...

REM Create directories
mkdir "app\src\main\res\mipmap-mdpi" 2>nul
mkdir "app\src\main\res\mipmap-hdpi" 2>nul
mkdir "app\src\main\res\mipmap-xhdpi" 2>nul
mkdir "app\src\main\res\mipmap-xxhdpi" 2>nul
mkdir "app\src\main\res\mipmap-xxxhdpi" 2>nul

echo تم إنشاء مجلدات الأيقونات
echo Icon directories created

echo.
echo ملاحظة: يجب إضافة ملفات PNG للأيقونات يدوياً أو استخدام أدوات التصميم
echo Note: PNG icon files need to be added manually or using design tools
echo.
echo الأحجام المطلوبة:
echo Required sizes:
echo - mipmap-mdpi: 48x48 px
echo - mipmap-hdpi: 72x72 px  
echo - mipmap-xhdpi: 96x96 px
echo - mipmap-xxhdpi: 144x144 px
echo - mipmap-xxxhdpi: 192x192 px
echo.
echo أسماء الملفات:
echo File names:
echo - ic_launcher.png
echo - ic_launcher_round.png
echo.
pause
